'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Level } from '@/types';
import { useLanguage } from '@/contexts/LanguageContext';

interface LevelSelectorProps {
  onLevelSelect: (level: Level) => void;
  onAuthClick?: () => void;
  onHistoryClick?: () => void;
}

export default function LevelSelector({ onLevelSelect }: LevelSelectorProps) {
  const { t } = useLanguage();
  const [loadingLevel, setLoadingLevel] = useState<Level | null>(null);

  const levels = [
    {
      id: 'beginner' as Level,
      title: t('beginner'),
      description: t('beginnerDesc'),
      color: 'gradient-secondary',
      hoverColor: 'bg-secondary-dark',
      icon: '🌱',
      gradient: '',
      accentColor: 'secondary-30',
      textColor: 'text-white'
    },
    {
      id: 'intermediate' as Level,
      title: t('intermediate'),
      description: t('intermediateDesc'),
      color: 'gradient-accent',
      hoverColor: 'bg-accent-dark',
      icon: '🌿',
      gradient: '',
      accentColor: 'accent-10',
      textColor: 'text-white'
    },
    {
      id: 'advanced' as Level,
      title: t('advanced'),
      description: t('advancedDesc'),
      color: 'gradient-primary-secondary',
      hoverColor: 'bg-primary-dark',
      icon: '🌳',
      gradient: '',
      accentColor: 'primary-60',
      textColor: 'text-white'
    }
  ];

  const handleLevelClick = async (level: Level) => {
    setLoadingLevel(level);
    await new Promise(resolve => setTimeout(resolve, 300));
    onLevelSelect(level);
  };

  return (
    <div className="min-h-screen relative overflow-hidden flex flex-col">
      {/* Modern Background with 60-30-10 colors */}
      <div className="absolute inset-0 gradient-primary"></div>
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-72 h-72 bg-secondary-30/20 rounded-full blur-3xl"></div>
        <div className="absolute top-20 right-0 w-96 h-96 bg-accent-10/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-1/4 w-80 h-80 bg-secondary-30/15 rounded-full blur-3xl"></div>
      </div>

      {/* Hero Section */}
      <div className="relative z-10 pt-16 pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-10 items-center mb-12 min-h-screen">
            {/* Left Content */}
            <div className="text-left">
              <div className="inline-flex items-center px-4 py-2 glass backdrop-blur-sm rounded-full text-sm font-medium text-secondary-30 mb-4 shadow-sm">
                <span className="w-2 h-2 bg-accent-10 rounded-full mr-2 animate-pulse"></span>
                AI-Powered Learning Platform
              </div>

              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-gray-900 via-secondary-30 to-accent-10 bg-clip-text text-transparent mb-4 leading-tight">
                {t('appTitle')}
              </h1>

              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                {t('appSubtitle')}
              </p>

              {/* Feature highlights */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-7 h-7 gradient-secondary rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs font-bold">∞</span>
                  </div>
                  <span className="text-base font-medium text-gray-700">Unlimited AI-Generated Content</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-7 h-7 bg-primary-dark rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs">🧠</span>
                  </div>
                  <span className="text-base font-medium text-gray-700">Smart Adaptive Learning</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-7 h-7 gradient-accent rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs">🌐</span>
                  </div>
                  <span className="text-base font-medium text-gray-700">Multi-Language Support</span>
                </div>
              </div>
            </div>

            {/* Right Banner Image */}
            <div className="relative lg:justify-self-end">
              <div className="relative">
                <Image
                  src="/images/banner.png"
                  alt="AI English Learning Platform"
                  width={400}
                  height={280}
                  className="w-full max-w-md h-auto object-cover"
                  priority
                />
                {/* Floating elements for visual appeal */}
                <div className="absolute -top-3 -right-3 w-6 h-6 bg-yellow-400 rounded-full animate-bounce delay-300"></div>
                <div className="absolute -bottom-3 -left-3 w-5 h-5 bg-pink-400 rounded-full animate-bounce delay-700"></div>
                <div className="absolute top-1/2 -left-6 w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="relative flex-1 pt-8 pb-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">

            {/* Stats Section */}
            <div className="flex justify-center mb-12">
              <div className="glass backdrop-blur-xl rounded-2xl px-8 py-6 shadow-xl border border-white/20">
                <div className="flex items-center space-x-8 text-sm">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-accent-10 mb-1">∞</div>
                    <div className="text-gray-600 font-medium">{t('questionsLabel')}</div>
                  </div>
                  <div className="w-px h-12 bg-gray-300"></div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-secondary-30 mb-1">3</div>
                    <div className="text-gray-600 font-medium">{t('languagesLabel')}</div>
                  </div>
                  <div className="w-px h-12 bg-gray-300"></div>
                  <div className="text-center">
                    <div className="text-3xl mb-1">🧠</div>
                    <div className="text-gray-600 font-medium">{t('aiPoweredLabel')}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Modern Level Cards */}
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mb-16">
            {levels.map((level) => {
              const isLoading = loadingLevel === level.id;
              return (
              <button
                key={level.id}
                onClick={() => handleLevelClick(level.id)}
                disabled={loadingLevel !== null}
                className="group relative glass backdrop-blur-xl border border-white/20 text-gray-800 p-8 rounded-3xl shadow-xl transform transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:-translate-y-2 focus:outline-none focus:ring-4 focus:ring-secondary-30/20 disabled:opacity-75 disabled:cursor-not-allowed disabled:transform-none overflow-hidden"
              >
                <div className={`absolute inset-0 ${level.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`}></div>

                <div className="relative z-10">
                  <div className={`w-16 h-16 ${level.color} rounded-2xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3`}>
                    <span className="text-3xl ${level.textColor}">{level.icon}</span>
                  </div>
                  
                  <h3 className="text-2xl font-bold mb-4 text-gray-800 group-hover:text-gray-900 transition-colors">
                    {level.title}
                  </h3>

                  <p className="text-gray-600 leading-relaxed font-medium mb-6 group-hover:text-gray-700 transition-colors">
                    {level.description}
                  </p>

                  <div className="flex justify-center">
                    <div className={`px-6 py-3 ${level.color} ${level.textColor} rounded-xl transition-all duration-300 group-hover:shadow-lg flex items-center space-x-2`}>
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                          <span className="text-sm font-medium">Loading...</span>
                        </>
                      ) : (
                        <>
                          <span className="text-sm font-medium">Start Learning</span>
                          <svg className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                          </svg>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="absolute top-4 right-4 w-8 h-8 bg-gray-100 rounded-full opacity-50 group-hover:opacity-100 transition-opacity"></div>
                <div className="absolute bottom-4 left-4 w-6 h-6 bg-gray-100 rounded-full opacity-30 group-hover:opacity-70 transition-opacity"></div>
              </button>
              );
            })}
          </div>

          {/* Modern Quick Access Section */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4">
                Explore Learning Paths
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Choose your preferred learning method and let AI create personalized content that adapts to your pace
              </p>
            </div>

            <div className="grid sm:grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
              <a
                href="/lessons"
                className="group relative bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-3xl p-8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-3 overflow-hidden"
              >
                {/* <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"white\" fill-opacity=\"0.05\"%3E%3Cpath d=\"M20 20c0 11.046-8.954 20-20 20v-40c11.046 0 20 8.954 20 20z\"/%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div> */}

                <div className="relative z-10 text-white">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3">
                    <span className="text-3xl">🎓</span>
                  </div>
                  <h3 className="text-2xl font-bold mb-4">{t('aiLessons')}</h3>
                  <p className="text-blue-100 mb-6 leading-relaxed">{t('lessonSubtitle')}</p>

                  <div className="flex items-center text-sm font-medium">
                    <span>Start Learning</span>
                    <svg className="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </div>
              </a>

              <a
                href="/reading"
                className="group relative bg-gradient-to-br from-purple-500 via-purple-600 to-pink-600 rounded-3xl p-8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-3 overflow-hidden"
              >
                {/* <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"white\" fill-opacity=\"0.05\"%3E%3Cpath d=\"M20 20c0 11.046-8.954 20-20 20v-40c11.046 0 20 8.954 20 20z\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div> */}

                <div className="relative z-10 text-white">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3">
                    <span className="text-3xl">📖</span>
                  </div>
                  <h3 className="text-2xl font-bold mb-4">{t('aiReading')}</h3>
                  <p className="text-purple-100 mb-6 leading-relaxed">{t('readingSubtitle')}</p>

                  <div className="flex items-center text-sm font-medium">
                    <span>Start Reading</span>
                    <svg className="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </div>
              </a>
            </div>
          </div>

          {/* Modern AI Tutor Section */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4">
                Meet Your AI English Tutor
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Get instant, personalized help 24/7. Ask questions in your native language and receive detailed explanations tailored to your learning level.
              </p>
            </div>

            <div className="max-w-4xl mx-auto">
              <a
                href="/tutor"
                className="group relative bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-600 rounded-3xl p-10 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-3 block overflow-hidden"
              >
                {/* <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"white\" fill-opacity=\"0.03\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"1.5\"/%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div> */}
                <div className="absolute top-10 right-10 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
                <div className="absolute bottom-10 left-10 w-24 h-24 bg-white/5 rounded-full blur-xl"></div>

                <div className="relative z-10">
                  <div className="grid lg:grid-cols-2 gap-8 items-center">
                    <div className="text-white">
                      <div className="flex items-center mb-6">
                        <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center mr-4 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3">
                          <span className="text-3xl">🤖</span>
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold">AI English Tutor</h3>
                          <p className="text-indigo-200 text-sm">Available 24/7</p>
                        </div>
                      </div>

                      <p className="text-indigo-100 mb-6 text-lg leading-relaxed">
                        Get instant help with grammar, vocabulary, pronunciation, and cultural context.
                        Ask questions in Vietnamese and receive personalized explanations.
                      </p>

                      <div className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-xl rounded-xl text-white font-medium transition-all duration-300 group-hover:bg-white/30 group-hover:scale-105">
                        <span>Start Conversation</span>
                        <svg className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 transition-transform duration-300 group-hover:scale-105">
                        <div className="text-2xl mb-2">📝</div>
                        <div className="text-white font-medium text-sm">Grammar Help</div>
                        <div className="text-indigo-200 text-xs">Rules & Examples</div>
                      </div>
                      <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 transition-transform duration-300 group-hover:scale-105 delay-75">
                        <div className="text-2xl mb-2">📚</div>
                        <div className="text-white font-medium text-sm">Vocabulary</div>
                        <div className="text-indigo-200 text-xs">Words & Meanings</div>
                      </div>
                      <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 transition-transform duration-300 group-hover:scale-105 delay-150">
                        <div className="text-2xl mb-2">🗣️</div>
                        <div className="text-white font-medium text-sm">Pronunciation</div>
                        <div className="text-indigo-200 text-xs">Sound & Accent</div>
                      </div>
                      <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 transition-transform duration-300 group-hover:scale-105 delay-225">
                        <div className="text-2xl mb-2">🌍</div>
                        <div className="text-white font-medium text-sm">Culture</div>
                        <div className="text-indigo-200 text-xs">Context & Usage</div>
                      </div>
                    </div>
                  </div>
                </div>
              </a>
            </div>
          </div>

          {/* Modern Features Section */}
          <div className="mb-20">
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4">
                {t('whyChooseTitle')}
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                {t('whyChooseSubtitle')}
              </p>
            </div>

            <div className="grid sm:grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="group relative bg-white/90 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3">
                    <span className="text-4xl text-white">∞</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-700 transition-colors">{t('aiGeneratedTitle')}</h3>
                  <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors">{t('aiGeneratedDesc')}</p>
                </div>

                <div className="absolute top-4 right-4 w-8 h-8 bg-blue-100 rounded-full opacity-50 group-hover:opacity-100 transition-opacity"></div>
              </div>

              <div className="group relative bg-white/90 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-teal-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3">
                    <span className="text-4xl text-white">🌐</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-emerald-700 transition-colors">{t('multiLanguageTitle')}</h3>
                  <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors">{t('multiLanguageDesc')}</p>
                </div>

                <div className="absolute top-4 right-4 w-8 h-8 bg-emerald-100 rounded-full opacity-50 group-hover:opacity-100 transition-opacity"></div>
              </div>

              <div className="group relative bg-white/90 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-600 rounded-3xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3">
                    <span className="text-4xl text-white">📊</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-orange-700 transition-colors">{t('progressTrackingTitle')}</h3>
                  <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors">{t('progressTrackingDesc')}</p>
                </div>

                <div className="absolute top-4 right-4 w-8 h-8 bg-orange-100 rounded-full opacity-50 group-hover:opacity-100 transition-opacity"></div>
              </div>
            </div>
          </div>

          {/* Modern Call-to-Action Section */}
          <div className="mt-16 text-center">
            <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-3xl p-12 shadow-2xl relative overflow-hidden">
              {/* <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"white\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"1.5\"/%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div> */}

              <div className="relative z-10 text-white">
                <h2 className="text-3xl sm:text-4xl font-bold mb-6">Ready to Start Learning?</h2>
                <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-8 leading-relaxed">
                  Join thousands of learners who are improving their English with AI-powered personalized content.
                  Choose your level and begin your journey today.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/lessons"
                    className="group px-8 py-4 bg-white/20 backdrop-blur-xl text-white rounded-2xl hover:bg-white/30 transition-all duration-300 font-medium text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center justify-center"
                  >
                    <span className="mr-2">🎓</span>
                    {t('createLesson')}
                    <svg className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </a>
                  <a
                    href="/reading"
                    className="group px-8 py-4 bg-white/20 backdrop-blur-xl text-white rounded-2xl hover:bg-white/30 transition-all duration-300 font-medium text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center justify-center"
                  >
                    <span className="mr-2">📖</span>
                    {t('generateReading')}
                    <svg className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
