'use client';

import { useState, useEffect, useRef } from 'react';
import { Question } from '@/types';
import { useLanguage } from '@/contexts/LanguageContext';
import MarkdownRenderer from './MarkdownRenderer';
import LoadingButton from './LoadingButton';

interface ResultCardProps {
  question: Question;
  selectedAnswer: number | null;
  isCorrect: boolean;
  onNextQuestion: () => void;
  score: {
    correct: number;
    total: number;
  };
}

export default function ResultCard({
  question,
  selectedAnswer,
  isCorrect,
  onNextQuestion,
  score
}: ResultCardProps) {
  const { t } = useLanguage();
  const [isLoadingNext, setIsLoadingNext] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const accuracy = score.total > 0 ? Math.round((score.correct / score.total) * 100) : 0;

  // Reset loading state if it takes too long
  useEffect(() => {
    if (isLoadingNext) {
      timeoutRef.current = setTimeout(() => {
        setIsLoadingNext(false);
      }, 10000); // Reset after 10 seconds
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isLoadingNext]);

  const handleNextQuestion = async () => {
    setIsLoadingNext(true);
    try {
      await onNextQuestion();
    } catch (error) {
      console.error('Error loading next question:', error);
      setIsLoadingNext(false);
    }
  };

  return (
    <div className="flex items-center justify-center p-3 sm:p-4 quiz-container">
      <div className="max-w-4xl w-full">
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 overflow-hidden">
          {/* Header with Result Status */}
          <div className={`px-4 sm:px-6 py-4 text-center relative overflow-hidden ${
            isCorrect
              ? 'bg-gradient-to-br from-emerald-50/60 via-green-50/60 to-teal-50/60'
              : 'bg-gradient-to-br from-red-50/60 via-pink-50/60 to-orange-50/60'
          }`}>
            <div className="relative z-10">
              <div className={`inline-flex items-center px-4 py-2 rounded-xl text-lg font-bold shadow-md border-2 backdrop-blur-sm ${
                isCorrect
                  ? 'bg-gradient-to-r from-emerald-50 to-green-50 text-emerald-700 border-emerald-200'
                  : 'bg-gradient-to-r from-red-50 to-pink-50 text-red-700 border-red-200'
              }`}>
                <span className="text-2xl mr-2">
                  {isCorrect ? '🎉' : '💪'}
                </span>
                {isCorrect ? t('correct') : t('incorrect')}
              </div>

              <div className="mt-3 flex justify-center">
                <div className="bg-white/90 backdrop-blur-xl rounded-xl px-4 py-2 border border-white/30 shadow-lg">
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="text-center">
                      <div className="text-xl font-bold text-emerald-600">{score.correct}</div>
                      <div className="text-gray-600 text-xs font-medium">{t('correct')}</div>
                    </div>
                    <div className="w-px h-6 bg-gray-300"></div>
                    <div className="text-center">
                      <div className="text-xl font-bold text-blue-600">{score.total}</div>
                      <div className="text-gray-600 text-xs font-medium">{t('total')}</div>
                    </div>
                    <div className="w-px h-6 bg-gray-300"></div>
                    <div className="text-center">
                      <div className={`text-xl font-bold ${accuracy >= 70 ? 'text-emerald-600' : accuracy >= 50 ? 'text-orange-600' : 'text-red-600'}`}>
                        {accuracy}%
                      </div>
                      <div className="text-gray-600 text-xs font-medium">{t('accuracy')}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-4 sm:p-6 quiz-content overflow-y-auto">
            {/* Question Review */}
            <div className="mb-4">
              <h3 className="text-lg font-bold text-gray-700 mb-3 flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-slate-400 to-slate-500 rounded-lg flex items-center justify-center mr-2">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                {t('question')}
              </h3>
              <div className="bg-gradient-to-r from-gray-50/80 to-blue-50/80 backdrop-blur-sm rounded-xl p-3 border border-gray-200/50 shadow-sm">
                <p className="text-gray-800 text-sm leading-relaxed font-medium">{question.question}</p>
              </div>
            </div>

            {/* Answers Comparison */}
            <div className="mb-4">
              <h3 className="text-lg font-bold text-gray-800 mb-2 flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-2">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                {t('answers')}
              </h3>

              <div className="space-y-3">
                {/* Your Answer */}
                <div className={`p-3 rounded-xl border-2 shadow-sm ${
                  isCorrect
                    ? 'bg-gradient-to-r from-emerald-50/70 to-green-50/70 border-emerald-200'
                    : 'bg-gradient-to-r from-red-50/70 to-pink-50/70 border-red-200'
                }`}>
                  <div className="flex items-center space-x-3">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm shadow-sm ${
                      isCorrect ? 'bg-gradient-to-br from-emerald-400 to-green-500' : 'bg-gradient-to-br from-red-400 to-pink-500'
                    }`}>
                      {isCorrect ? '✓' : '✗'}
                    </div>
                    <div className="flex-1">
                      <div className="font-bold text-gray-600 text-xs mb-1 flex items-center">
                        <span className="mr-1">👤</span>
                        {t('yourAnswer')}
                      </div>
                      <div className={`text-sm font-semibold ${isCorrect ? 'text-emerald-700' : 'text-red-700'}`}>
                        {selectedAnswer !== null ? question.options[selectedAnswer] : 'No answer selected'}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Correct Answer (if wrong) */}
                {!isCorrect && (
                  <div className="p-3 rounded-xl border-2 bg-gradient-to-r from-emerald-50/70 to-green-50/70 border-emerald-200 shadow-sm">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-emerald-400 to-green-500 rounded-lg flex items-center justify-center text-white font-bold text-sm shadow-sm">
                        ✓
                      </div>
                      <div className="flex-1">
                        <div className="font-bold text-gray-600 text-xs mb-1 flex items-center">
                          <span className="mr-1">🎯</span>
                          {t('correctAnswer')}
                        </div>
                        <div className="text-sm font-semibold text-emerald-700">
                          {question.correctAnswer !== undefined ? question.options[question.correctAnswer] : 'N/A'}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Explanation */}
            <div className="mb-4">
              <h3 className="text-lg font-bold text-gray-700 mb-3 flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-slate-400 to-slate-500 rounded-lg flex items-center justify-center mr-2">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                {t('explanation')}
              </h3>
              <div className="bg-gradient-to-r from-slate-50/60 to-gray-50/60 backdrop-blur-sm border-l-4 border-slate-300 p-4 rounded-r-xl shadow-sm">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-slate-400 rounded-lg flex items-center justify-center mt-0.5">
                    <span className="text-white text-xs">💡</span>
                  </div>
                  <div className="flex-1">
                    <MarkdownRenderer
                      content={question.explanation || ''}
                      className="text-gray-600"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Next Question Button */}
            <div className="text-center">
              <LoadingButton
                onClick={handleNextQuestion}
                isLoading={isLoadingNext}
                loadingText={t('generatingQuestion') || 'Generating...'}
                className="group bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-600 hover:from-blue-600 hover:via-indigo-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-blue-300/50 transform hover:-translate-y-0.5"
              >
                <span className="flex items-center space-x-2">
                  <span className="text-sm">{t('nextQuestion')}</span>
                  <div className="w-5 h-5 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors duration-300">
                    <svg className="w-3 h-3 transform group-hover:translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </span>
              </LoadingButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
